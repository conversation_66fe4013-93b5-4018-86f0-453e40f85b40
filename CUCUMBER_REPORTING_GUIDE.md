# 🥒 Cucumber HTML Reporting Guide

Hướng dẫn sử dụng thư viện `net.masterthought:cucumber-reporting:5.8.1` để tạo báo cáo HTML đẹp cho Cucumber tests.

## 📋 Tổng quan

Thư viện `cucumber-reporting` giúp chuyển đổi file JSON kết quả từ Cucumber thành báo cáo HTML chuyên nghiệp với:

- 📊 **Dashboard tổng quan** với thống kê chi tiết
- 📈 **Biểu đồ và charts** trực quan
- 🔍 **Chi tiết từng scenario** và steps
- ⏱️ **Thời gian thực thi** của từng test
- 📱 **Responsive design** cho mobile
- 🎨 **Giao diện chuyên nghiệp**

## 🚀 Cách sử dụng

### 1. Chạy Cucumber tests và tạo báo cáo

```bash
# Chạy tất cả cucumber tests và tự động tạo HTML report
./gradlew cucumberTestWithReport

# Hoặc chạy từng bước:
./gradlew test --tests StepUpAuthApplicationCucumberBase
./gradlew generateCucumberReport
```

### 2. Mở báo cáo trong browser

```bash
# Tự động mở báo cáo trong browser
./gradlew openCucumberReport
```

### 3. Xem báo cáo thủ công

Mở file: `build/reports/cucumber-html-reports/cucumber-html-reports/overview-features.html`

## 📁 Cấu trúc báo cáo

```
build/reports/cucumber-html-reports/cucumber-html-reports/
├── overview-features.html    # 📊 Trang chính - tổng quan features
├── overview-steps.html       # 👣 Tổng quan các steps
├── overview-tags.html        # 🏷️ Tổng quan theo tags
├── overview-failures.html    # ❌ Tổng quan lỗi
├── report-feature_*.html     # 📄 Chi tiết từng feature
├── css/                      # 🎨 Styles
├── js/                       # ⚡ JavaScript
├── images/                   # 🖼️ Hình ảnh
└── fonts/                    # 🔤 Fonts
```

## 🛠️ Cấu hình

### Gradle Tasks

- `generateCucumberReport` - Tạo HTML report từ JSON
- `cucumberTestWithReport` - Chạy tests + tạo report
- `openCucumberReport` - Mở report trong browser

### Tùy chỉnh báo cáo

Chỉnh sửa file `src/test/java/com/tyme/tymex/stepupauth/utils/CucumberReportGenerator.java`:

```java
// Thay đổi tên project
private static final String PROJECT_NAME = "Your Project Name";

// Thêm metadata
configuration.addClassifications("Environment", "Production");
configuration.addClassifications("Browser", "Chrome");

// Cấu hình sorting
configuration.setSortingMethod(SortingMethod.ALPHABETICAL);
```

## 📊 Các trang báo cáo

### 1. Overview Features
- Tổng quan tất cả features
- Thống kê pass/fail
- Thời gian thực thi
- Biểu đồ pie chart

### 2. Overview Steps
- Chi tiết từng step definition
- Tần suất sử dụng
- Thời gian thực thi trung bình

### 3. Overview Tags
- Nhóm theo tags
- Thống kê theo từng tag

### 4. Overview Failures
- Danh sách tất cả lỗi
- Chi tiết stack trace
- Screenshots (nếu có)

## 🎯 Ví dụ sử dụng

### Chạy demo

```bash
# Chạy demo để xem thư viện hoạt động
./gradlew test --tests CucumberReportDemo

# Chạy cucumber tests thực tế
./gradlew test --tests StepUpAuthApplicationCucumberBase

# Tạo báo cáo HTML
./gradlew generateCucumberReport

# Mở báo cáo
./gradlew openCucumberReport
```

### Tích hợp vào CI/CD

```yaml
# GitHub Actions example
- name: Run Cucumber Tests
  run: ./gradlew cucumberTestWithReport

- name: Upload Reports
  uses: actions/upload-artifact@v3
  with:
    name: cucumber-reports
    path: build/reports/cucumber-html-reports/
```

## 🔧 Troubleshooting

### Lỗi thường gặp

1. **File JSON không tồn tại**
   ```
   Solution: Chạy cucumber tests trước khi generate report
   ./gradlew test --tests StepUpAuthApplicationCucumberBase
   ```

2. **Báo cáo trống**
   ```
   Solution: Kiểm tra cấu hình cucumber trong StepUpAuthApplicationCucumberBase
   ```

3. **Không mở được browser**
   ```
   Solution: Mở thủ công file overview-features.html
   ```

## 📚 Tài liệu tham khảo

- [Cucumber Reporting GitHub](https://github.com/damianszczepanik/cucumber-reporting)
- [Cucumber Documentation](https://cucumber.io/docs/)
- [Maven Repository](https://mvnrepository.com/artifact/net.masterthought/cucumber-reporting)

## ✨ Tính năng nổi bật

- ✅ **Tự động tạo báo cáo** sau khi chạy tests
- ✅ **Giao diện đẹp** và chuyên nghiệp  
- ✅ **Responsive design** cho mọi thiết bị
- ✅ **Chi tiết đầy đủ** về test results
- ✅ **Dễ tích hợp** vào CI/CD pipeline
- ✅ **Hỗ trợ nhiều format** (HTML, JSON)
