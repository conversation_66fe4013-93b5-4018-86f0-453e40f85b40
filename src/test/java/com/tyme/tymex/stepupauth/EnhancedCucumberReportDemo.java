package com.tyme.tymex.stepupauth;

import com.tyme.tymex.stepupauth.utils.AdvancedCucumberReportBuilder;
import com.tyme.tymex.stepupauth.utils.CucumberReportGenerator;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Enhanced Demo class showcasing advanced Cucumber reporting features
 * with multiple report formats, custom themes, and enhanced analytics
 */
@Log4j2
public class EnhancedCucumberReportDemo {

    private static final String JSON_REPORT_PATH = "build/reports/cucumber-report.json";
    private static final String OUTPUT_DIR = "build/reports/cucumber-html-reports";
    private static final String ENHANCED_OUTPUT_DIR = "build/reports/enhanced-cucumber-reports";

    @Test
    @Order(1)
    @DisplayName("🚀 Basic Enhanced Report Generation")
    public void demonstrateBasicEnhancedReport() {
        log.info("=== 🚀 Basic Enhanced Report Generation Demo ===");
        
        File jsonFile = new File(JSON_REPORT_PATH);
        if (jsonFile.exists()) {
            log.info("✅ Found JSON report: {} ({})", JSON_REPORT_PATH, formatFileSize(jsonFile.length()));
            
            try {
                // Generate enhanced report with additional features
                CucumberReportGenerator.generateDefaultReport();
                CucumberReportGenerator.printSummary(OUTPUT_DIR);
                
                log.info("✅ Basic enhanced report generated successfully!");
                
            } catch (Exception e) {
                log.error("❌ Failed to generate basic enhanced report: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️ No JSON report found. Run cucumber tests first:");
            log.info("   ./gradlew test --tests StepUpAuthApplicationCucumberBase");
        }
    }

    @Test
    @Order(2)
    @DisplayName("🎨 Advanced Report with Custom Features")
    public void demonstrateAdvancedReportBuilder() {
        log.info("=== 🎨 Advanced Report Builder Demo ===");
        
        File jsonFile = new File(JSON_REPORT_PATH);
        if (jsonFile.exists()) {
            try {
                // Create advanced report with builder pattern
                AdvancedCucumberReportBuilder.ReportResult result = new AdvancedCucumberReportBuilder("Step-Up Auth Service")
                    .withOutputDirectory(ENHANCED_OUTPUT_DIR)
                    .withJsonFile(JSON_REPORT_PATH)
                    .withClassification("🌟 Test Suite", "Step-Up Authentication")
                    .withClassification("🔧 Test Type", "Integration & E2E")
                    .withClassification("🌍 Environment", "Test Environment")
                    .withClassification("🏷️ Version", "v2.0.0-enhanced")
                    .withClassification("👥 Team", "QA Engineering")
                    .withClassification("📧 Contact", "<EMAIL>")
                    .withTrends(true)
                    .withNotifications(false)
                    .withTheme("professional")
                    .build();
                
                log.info("✅ Advanced report generated successfully!");
                log.info("📊 Summary report: {}", result.getSummaryReportPath());
                log.info("📈 Trends enabled: {}", result.isTrendAnalysisEnabled());
                log.info("⚡ Performance metrics: {}", result.isPerformanceMetricsGenerated());
                log.info("📋 Custom dashboard: {}", result.isCustomDashboardGenerated());
                
            } catch (Exception e) {
                log.error("❌ Failed to generate advanced report: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️ No JSON report found for advanced demo");
        }
    }

    @Test
    @Order(3)
    @DisplayName("📊 Multiple JSON Files Report")
    public void demonstrateMultipleJsonFilesReport() {
        log.info("=== 📊 Multiple JSON Files Report Demo ===");
        
        // Find all JSON files in reports directory
        List<String> jsonFiles = findJsonFiles("build/reports");
        
        if (!jsonFiles.isEmpty()) {
            log.info("🔍 Found {} JSON files:", jsonFiles.size());
            jsonFiles.forEach(file -> log.info("   📄 {}", file));
            
            try {
                // Generate report from multiple JSON files
                CucumberReportGenerator.generateReportFromDirectory("build/reports", "build/reports/multi-json-reports");
                
                log.info("✅ Multi-JSON report generated successfully!");
                
            } catch (Exception e) {
                log.error("❌ Failed to generate multi-JSON report: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️ No JSON files found in build/reports directory");
        }
    }

    @Test
    @Order(4)
    @DisplayName("🎯 Report Features Showcase")
    public void showcaseReportFeatures() {
        log.info("=== 🎯 Enhanced Report Features Showcase ===");
        
        log.info("📋 ==================== ENHANCED FEATURES ====================");
        log.info("🎨 Custom Themes:");
        log.info("   • Professional theme with modern styling");
        log.info("   • Dark mode support");
        log.info("   • Mobile-responsive design");
        log.info("   • Custom color schemes");
        
        log.info("📊 Advanced Analytics:");
        log.info("   • Trend analysis across multiple runs");
        log.info("   • Performance metrics and timing");
        log.info("   • Failure pattern analysis");
        log.info("   • Test execution statistics");
        
        log.info("🔧 Enhanced Configuration:");
        log.info("   • Builder pattern for easy setup");
        log.info("   • Custom classifications and metadata");
        log.info("   • Multiple output formats");
        log.info("   • Flexible file discovery");
        
        log.info("📈 Additional Reports:");
        log.info("   • Executive summary dashboard");
        log.info("   • Detailed performance metrics");
        log.info("   • Custom HTML summaries");
        log.info("   • Trend analysis charts");
        
        log.info("🔔 Integration Features:");
        log.info("   • Email notification support");
        log.info("   • Slack integration ready");
        log.info("   • CI/CD pipeline friendly");
        log.info("   • Automated report archiving");
        
        log.info("================================================================");
    }

    @Test
    @Order(5)
    @DisplayName("🛠️ Report Validation and Health Check")
    public void validateReportHealth() {
        log.info("=== 🛠️ Report Health Check ===");
        
        // Check main report
        checkReportFile(OUTPUT_DIR + "/cucumber-html-reports/overview-features.html", "Main Features Report");
        checkReportFile(OUTPUT_DIR + "/cucumber-html-reports/overview-steps.html", "Steps Report");
        checkReportFile(OUTPUT_DIR + "/cucumber-html-reports/overview-tags.html", "Tags Report");
        checkReportFile(OUTPUT_DIR + "/cucumber-html-reports/overview-failures.html", "Failures Report");
        
        // Check enhanced reports
        checkReportFile(ENHANCED_OUTPUT_DIR + "/cucumber-summary.html", "Enhanced Summary Report");
        
        // Check JSON source
        checkReportFile(JSON_REPORT_PATH, "Source JSON Report");
        
        log.info("🏥 Health check completed!");
    }

    @Test
    @Order(6)
    @DisplayName("📚 Usage Instructions")
    public void showUsageInstructions() {
        log.info("=== 📚 Enhanced Usage Instructions ===");
        
        log.info("🚀 Quick Start:");
        log.info("   ./gradlew cucumberTestWithReport");
        log.info("   ./gradlew openCucumberReport");
        
        log.info("🎨 Advanced Usage:");
        log.info("   ./gradlew test --tests StepUpAuthApplicationCucumberBase");
        log.info("   ./gradlew test --tests EnhancedCucumberReportDemo");
        log.info("   ./gradlew generateCucumberReport");
        
        log.info("📊 Multiple Reports:");
        log.info("   CucumberReportGenerator.generateReportFromDirectory(\"reports\", \"output\");");
        
        log.info("🏗️ Builder Pattern:");
        log.info("   new AdvancedCucumberReportBuilder(\"Project\")");
        log.info("       .withJsonFile(\"report.json\")");
        log.info("       .withTheme(\"professional\")");
        log.info("       .withTrends(true)");
        log.info("       .build();");
        
        log.info("📁 File Locations:");
        log.info("   • JSON Reports: build/reports/");
        log.info("   • HTML Reports: build/reports/cucumber-html-reports/");
        log.info("   • Enhanced Reports: build/reports/enhanced-cucumber-reports/");
        log.info("   • Documentation: CUCUMBER_REPORTING_GUIDE.md");
    }

    // Helper methods
    private List<String> findJsonFiles(String directory) {
        try {
            return Files.walk(Paths.get(directory))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .filter(path -> path.toString().contains("cucumber"))
                .map(path -> path.toString())
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.warn("⚠️ Error finding JSON files: {}", e.getMessage());
            return Arrays.asList();
        }
    }

    private void checkReportFile(String filePath, String reportName) {
        File file = new File(filePath);
        if (file.exists()) {
            log.info("✅ {} - {} ({})", reportName, filePath, formatFileSize(file.length()));
        } else {
            log.warn("❌ {} - Not found: {}", reportName, filePath);
        }
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
