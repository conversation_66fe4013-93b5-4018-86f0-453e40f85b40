package com.tyme.tymex.stepupauth;

import com.tyme.tymex.stepupauth.utils.CucumberReportGenerator;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

/**
 * Demo class to show how to generate Cucumber HTML reports
 * using net.masterthought:cucumber-reporting library
 */
@Log4j2
@SpringBootTest
public class CucumberReportDemo {

    @Test
    public void demonstrateCucumberReportGeneration() {
        log.info("=== Cucumber Report Generation Demo ===");
        
        // Check if JSON report exists
        String jsonReportPath = "build/reports/cucumber-report.json";
        File jsonFile = new File(jsonReportPath);
        
        if (jsonFile.exists()) {
            log.info("Found existing cucumber JSON report at: {}", jsonReportPath);
            
            // Generate HTML report
            try {
                CucumberReportGenerator.generateDefaultReport();
                log.info("✅ HTML report generated successfully!");
                log.info("📊 Open the report at: build/reports/cucumber-html-reports/overview-features.html");
            } catch (Exception e) {
                log.error("❌ Failed to generate report: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️  No cucumber JSON report found at: {}", jsonReportPath);
            log.info("💡 To generate a report, first run cucumber tests:");
            log.info("   ./gradlew test --tests StepUpAuthApplicationCucumberBase");
            log.info("   Then run this demo again to generate the HTML report");
        }
    }
    
    @Test
    public void showReportFeatures() {
        log.info("=== Cucumber HTML Report Features ===");
        log.info("📈 Dashboard with test statistics");
        log.info("📊 Charts and graphs for test results");
        log.info("🔍 Detailed scenario breakdown");
        log.info("⏱️  Execution time tracking");
        log.info("📱 Responsive design for mobile viewing");
        log.info("🎨 Professional styling and layout");
        log.info("📋 Test metadata and classifications");
        log.info("🔗 Clickable navigation between features");
    }
}
