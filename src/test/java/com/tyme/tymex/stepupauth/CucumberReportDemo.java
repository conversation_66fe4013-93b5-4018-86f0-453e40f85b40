package com.tyme.tymex.stepupauth;

import com.tyme.tymex.stepupauth.utils.CucumberReportGenerator;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;

import java.io.File;

/**
 * Simple demo to verify Cucumber HTML report generation
 */
@Log4j2
public class CucumberReportDemo {

    @Test
    public void verifyCucumberReportGeneration() {
        log.info("=== Verifying Cucumber Report Generation ===");

        String jsonReportPath = "build/reports/cucumber-report.json";
        File jsonFile = new File(jsonReportPath);

        if (jsonFile.exists()) {
            log.info("✅ JSON report found: {} ({})", jsonReportPath, formatFileSize(jsonFile.length()));

            try {
                CucumberReportGenerator.generateDefaultReport();
                log.info("✅ HTML report generated successfully!");
                log.info("📊 Main report: build/reports/cucumber-html-reports/cucumber-html-reports/overview-features.html");
            } catch (Exception e) {
                log.error("❌ Failed to generate report: {}", e.getMessage());
            }
        } else {
            log.warn("⚠️ No JSON report found. Run cucumber tests first:");
            log.info("   ./gradlew test --tests StepUpAuthApplicationCucumberBase");
        }
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
