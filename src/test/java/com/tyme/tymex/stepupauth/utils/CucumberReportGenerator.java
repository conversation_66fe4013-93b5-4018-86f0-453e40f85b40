package com.tyme.tymex.stepupauth.utils;

import net.masterthought.cucumber.Configuration;
import net.masterthought.cucumber.ReportBuilder;
import net.masterthought.cucumber.sorting.SortingMethod;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to generate beautiful HTML reports from Cucumber JSON results
 * using net.masterthought:cucumber-reporting library
 */
@Log4j2
public class CucumberReportGenerator {

    private static final String PROJECT_NAME = "Step-Up Authentication Service";
    private static final String BUILD_NUMBER = "1.0.0";
    
    /**
     * Generate HTML report from cucumber JSON file
     * 
     * @param jsonReportPath Path to cucumber JSON report file
     * @param outputDirectory Directory where HTML report will be generated
     */
    public static void generateReport(String jsonReportPath, String outputDirectory) {
        try {
            File reportOutputDirectory = new File(outputDirectory);
            
            // Create output directory if it doesn't exist
            if (!reportOutputDirectory.exists()) {
                reportOutputDirectory.mkdirs();
            }
            
            // List of JSON files to process
            List<String> jsonFiles = new ArrayList<>();
            jsonFiles.add(jsonReportPath);
            
            // Configuration for the report
            Configuration configuration = new Configuration(reportOutputDirectory, PROJECT_NAME);
            
            // Set build number
            configuration.setBuildNumber(BUILD_NUMBER);
            
            // Add additional metadata
            configuration.addClassifications("Platform", "Spring Boot");
            configuration.addClassifications("Environment", "Test");
            configuration.addClassifications("Java Version", System.getProperty("java.version"));
            configuration.addClassifications("OS", System.getProperty("os.name"));
            
            // Configure sorting
            configuration.setSortingMethod(SortingMethod.NATURAL);
            
            // Generate the report
            ReportBuilder reportBuilder = new ReportBuilder(jsonFiles, configuration);
            reportBuilder.generateReports();
            
            log.info("Cucumber HTML report generated successfully at: {}/cucumber-html-reports/overview-features.html", 
                    outputDirectory);
            
        } catch (Exception e) {
            log.error("Failed to generate cucumber report", e);
            throw new RuntimeException("Failed to generate cucumber report", e);
        }
    }
    
    /**
     * Generate report with default paths
     */
    public static void generateDefaultReport() {
        String jsonPath = "build/reports/cucumber-report.json";
        String outputPath = "build/reports/cucumber-html-reports";
        generateReport(jsonPath, outputPath);
    }
    
    /**
     * Main method for standalone execution
     */
    public static void main(String[] args) {
        if (args.length >= 2) {
            generateReport(args[0], args[1]);
        } else {
            generateDefaultReport();
        }
    }
}
