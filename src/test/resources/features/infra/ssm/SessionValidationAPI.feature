# language: en
Feature: Validate a Step-Up Session
  As a client application
  I want to validate an existing step-up session
  So that I can retrieve its details and factor configuration if needed.

  Scenario Outline: Successfully validate an active step-up session and retrieve details
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    And A step-up session has been initialized with factor <auth> for profile <profile> using config <config> and the auth ID is saved
    When I validate the saved step-up session for factor <validationFactor> and request factor config <includeConfig>
    Then The validation response should be successful
    And The validation response should contain correct session details for original factor <auth> and profile <profile>
    And If <includeConfig> is true, the validation response should contain the configuration for factor <validationFactor> matching <expectedConfigValidation>
    And If <includeConfig> is false, the validation response should NOT contain any factor configuration
    Examples:
      | dataType | auth          | config                                                                                                                                             | validationFactor | includeConfig | expectedConfigValidation   |
      | phone    | OTP           | channel:SMS,dialCode:+63,cellphone:+632134422221,purpose:verify_your_cellphone_number                                                              | OTP              | true          | enriched with profile data |
      | phone    | OTP           | channel:SMS,dialCode:+63,cellphone:+632134422221                                                                                                   | OTP              | false         |                            |
      | device   | DEVICE_BIO    | deviceId:ad4c48c67836ff97,internalDeviceId:ad4c48c67836ff97,linkDeviceDays:1,deviceBioEnrollDays:1                                                 | DEVICE_BIO       | true          | provided device data       |
      | device   | PASSCODE      | deviceId:ad4c48c67836ff97                                                                                                                          | PASSCODE         | true          | device information         |
      | facial   | FACIAL        | numberOfLivenessCheck:5,numberOfComparison:3,enrollment:INACTIVE,segmentTracking:INACTIVE,triggerPoint:INTRO,type:SELFIE_LIVENESS_CHECK_COMPARISON | FACIAL           | true          | facial recognition config  |


  Scenario Outline: Successfully validate an active step-up session and retrieve details
    Given A user profile exists in the system
    And The profile contains valid "<dataType>" information
    When I create a step-up session using Factor "<auth>" authentication method with Profile Type: "<identifierType>" and config "<authConfigParams>"
    Then The step-up session should be successfully created
    When I validate the saved step-up session for factor <validationFactor> and request factor config <includeConfig>
    Then The validation response should be successful
    And The validation response should contain correct session details for original factor <auth>
    Then The validation response should <configPresence> factor configuration for <validationFactor> matching <expectedConfigValidation>
    Examples:
      | dataType | auth          |identifierType| authConfigParams                                 | validationFactor | includeConfig | expectedConfigValidation   |configPresence|
      | phone    | OTP           |PROFILE_ID    | /dataShare/failCase/OTP_authConfig.json          | OTP              | true          | enriched with profile data |contain       |
      | phone    | OTP           |PROFILE_ID    | /dataShare/failCase/OTP_NoPHONE_authConfig.json  | OTP              | false         |                            |NOT contain   |
      | device   | DEVICE_BIO    |PROFILE_ID    | /dataShare/failCase/DEVICE_BIO_authConfig.json   | DEVICE_BIO       | true          | provided device data       |contain       |
      | device   | PASSCODE      |PROFILE_ID    | /dataShare/failCase/PASS_CODE_authConfig.json    | PASSCODE         | true          | device information         |contain       |
