plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.12'
    id 'io.spring.dependency-management' version '1.1.5'
    id 'org.asciidoctor.jvm.convert' version '4.0.2'
    id 'jacoco'
    id 'org.sonarqube' version '5.0.0.4638'
}

group = 'com.tyme.tymex'
version = '1.0.6'

java {
    sourceCompatibility = '21'
    targetCompatibility = '21'
}

configurations {
    all*.exclude module: 'spring-boot-starter-logging'
    compileOnly {
        extendsFrom annotationProcessor
    }
}


repositories {
    mavenLocal()
    mavenCentral {
        content {
            excludeGroupByRegex "com\\.tyme.*"
        }
    }
    maven {
        url 'https://plugins.gradle.org/m2/'
        content {
            excludeGroupByRegex "com\\.tyme.*"
        }
    }

    maven {
        url "https://${System.env.CODEARTIFACT_DOMAIN}-${System.env.AWS_ACCOUNT_ID}.d.codeartifact.${System.env.AWS_REGION}.amazonaws.com/maven/libs-release/"
        credentials {
            username 'aws'
            password System.env.CODEARTIFACT_AUTH_TOKEN
        }
    }

}

jar {
    enabled = false
}

ext {
    set('snippetsDir', file('build/generated-snippets'))
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation('org.springframework.boot:spring-boot-starter-web') {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    implementation 'net.masterthought:cucumber-reporting:5.8.1'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    //Apache common codecs
    implementation 'commons-codec:commons-codec:1.17.1'


    //SpringCloud
    implementation 'io.awspring.cloud:spring-cloud-aws-starter-dynamodb'
    implementation 'io.awspring.cloud:spring-cloud-aws-starter-sqs'

    //SpringDocs
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${springdocVersion}"

    //AWS SDK
    implementation platform("software.amazon.awssdk:bom:${awsSdkVersion}")
    implementation 'software.amazon.awssdk:dynamodb'
    implementation 'software.amazon.awssdk:ssm'
    implementation 'software.amazon.awssdk:secretsmanager'
    implementation 'software.amazon.awssdk:dynamodb'
    implementation 'software.amazon.awssdk:auth'


    //Open feign
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-openfeign-core'

    implementation "com.nimbusds:nimbus-jose-jwt:${nimbusJwtVersion}"
    implementation "com.google.guava:guava:${guavaVersion}"

    //Tomcat embed
    implementation "org.apache.tomcat.embed:tomcat-embed-core:${tomcatEmbedCore}"
    implementation "org.apache.tomcat.embed:tomcat-embed-websocket:${tomcatEmbedWebsocket}"

    //Testing
    implementation "io.cucumber:cucumber-jvm:${cucumberVersion}"
    implementation "io.cucumber:cucumber-core:${cucumberVersion}"
    implementation "io.cucumber:cucumber-java:${cucumberVersion}"
    implementation "io.cucumber:cucumber-spring:${cucumberVersion}"
    testImplementation "io.cucumber:cucumber-junit-platform-engine:${cucumberVersion}"
    testImplementation "io.cucumber:cucumber-junit:${cucumberVersion}"
    testImplementation 'tech.grasshopper:extentreports-cucumber7-adapter:1.5.0'

    //Common libs
    implementation 'com.fasterxml.uuid:java-uuid-generator:5.1.0'
    //AWS SDK
    implementation platform("software.amazon.awssdk:bom:${awsSdkVersion}")
    implementation 'software.amazon.awssdk:dynamodb'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testAnnotationProcessor "org.projectlombok:lombok"
    testImplementation 'org.projectlombok:lombok:1.18.28'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.restdocs:spring-restdocs-webtestclient'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.testcontainers:testcontainers'
    testImplementation "org.testcontainers:junit-jupiter"
    testImplementation 'org.testcontainers:localstack'
    testImplementation 'org.mockito:mockito-core:5.9.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.9.0'
    testImplementation 'com.squareup.okhttp3:okhttp:4.12.0'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
    testImplementation 'org.wiremock:wiremock:3.13.0'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.restdocs:spring-restdocs-webtestclient'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'

    testImplementation 'org.junit.platform:junit-platform-suite:1.10.3'
    testImplementation 'org.junit.platform:junit-platform-runner:1.10.3'

    testImplementation 'org.testcontainers:testcontainers'
    testImplementation 'org.testcontainers:localstack'

    testImplementation 'org.mockito:mockito-core:5.9.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.9.0'

    testImplementation 'com.squareup.okhttp3:okhttp:4.12.0'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "io.awspring.cloud:spring-cloud-aws-dependencies:${springCloudAwsVersion}"
    }
}

sonarqube {
    properties {
        property 'sonar.organization', 'tymerepos'
        property 'sonar.host.url', 'https://sonarcloud.io'
        property 'sonar.qualitygate.wait', 'true'
    }
}

if (hasProperty('buildScan')) {
    buildScan {
        termsOfServiceUrl = 'https://gradle.com/terms-of-service'
        termsOfServiceAgree = 'no'
    }
}

jacocoTestReport {
	reports {
		xml.required.set(true)
	}
}

tasks['test'].finalizedBy jacocoTestReport
tasks['sonarqube'].dependsOn jacocoTestReport

tasks.named('test') {
    outputs.dir snippetsDir
    useJUnitPlatform()
}

// Task to generate Cucumber HTML reports
task generateCucumberReport(type: JavaExec) {
    group = 'reporting'
    description = 'Generate beautiful HTML reports from Cucumber JSON results'

    classpath = sourceSets.test.runtimeClasspath
    mainClass = 'com.tyme.tymex.stepupauth.utils.CucumberReportGenerator'

    // Only run if JSON report exists
    onlyIf {
        file('build/reports/cucumber-report.json').exists()
    }

    doFirst {
        println "🚀 Generating Cucumber HTML Report..."
    }

    doLast {
        println "✅ Cucumber HTML Report generated!"
        println "📊 Open: build/reports/cucumber-html-reports/overview-features.html"
    }
}

// Task to run cucumber tests and generate report
task cucumberTestWithReport {
    group = 'verification'
    description = 'Run Cucumber tests and generate HTML report'

    dependsOn test
    finalizedBy generateCucumberReport
}

// Task to open the generated report in browser
task openCucumberReport {
    group = 'reporting'
    description = 'Open the generated Cucumber HTML report in browser'

    dependsOn generateCucumberReport

    doLast {
        def reportFile = file('build/reports/cucumber-html-reports/cucumber-html-reports/overview-features.html')
        if (reportFile.exists()) {
            println "🌐 Opening Cucumber Report in browser..."

            def os = System.getProperty('os.name').toLowerCase()
            if (os.contains('mac')) {
                exec { commandLine 'open', reportFile.absolutePath }
            } else if (os.contains('windows')) {
                exec { commandLine 'cmd', '/c', 'start', reportFile.absolutePath }
            } else {
                exec { commandLine 'xdg-open', reportFile.absolutePath }
            }
        } else {
            println "❌ Report file not found. Run 'generateCucumberReport' first."
        }
    }
}

// Enhanced Cucumber Reporting Tasks
task enhancedCucumberReport(type: JavaExec) {
    group = 'reporting'
    description = 'Generate enhanced Cucumber HTML reports with advanced features'

    classpath = sourceSets.test.runtimeClasspath
    mainClass = 'com.tyme.tymex.stepupauth.utils.AdvancedCucumberReportBuilder'

    doFirst {
        println "🚀 Generating Enhanced Cucumber Report with advanced features..."
    }

    doLast {
        println "✅ Enhanced Cucumber Report generated!"
        println "📊 Check: build/reports/enhanced-cucumber-reports/"
    }
}

task runEnhancedDemo(type: Test) {
    group = 'verification'
    description = 'Run enhanced Cucumber reporting demo with all features'

    useJUnitPlatform()
    include '**/EnhancedCucumberReportDemo.class'

    testLogging {
        events "passed", "skipped", "failed"
        showStandardStreams = true
    }

    doFirst {
        println "🎨 Running Enhanced Cucumber Report Demo..."
    }
}

task cucumberReportingShowcase {
    group = 'reporting'
    description = 'Complete showcase: Run tests, generate reports, and demo features'

    dependsOn test
    dependsOn generateCucumberReport
    dependsOn enhancedCucumberReport
    finalizedBy runEnhancedDemo

    doLast {
        println ""
        println "🎉 ==================== CUCUMBER REPORTING SHOWCASE COMPLETE ===================="
        println "📊 Standard Reports: build/reports/cucumber-html-reports/"
        println "🎨 Enhanced Reports: build/reports/enhanced-cucumber-reports/"
        println "📋 Summary: build/reports/enhanced-cucumber-reports/cucumber-summary.html"
        println "📚 Guide: CUCUMBER_REPORTING_GUIDE.md"
        println "================================================================================"
    }
}

tasks.named('asciidoctor') {
    inputs.dir snippetsDir
    dependsOn test
}